"""
Main entry point for the class imbalance agent.
"""
import os
import argparse
import pandas as pd
import numpy as np
import json
from dotenv import load_dotenv

from agent import run_workflow

def load_env_file():
    """Load environment variables from .env file."""
    if not os.path.exists('.env'):
        raise FileNotFoundError("Missing .env file with GROQ_API_KEY")

    load_dotenv()

    if not os.environ.get('GROQ_API_KEY'):
        raise ValueError("Missing GROQ_API_KEY in .env file")

def generate_sample_dataset(output_path: str, imbalance_ratio: float = 10.0) -> str:
    """Generate a sample imbalanced dataset."""
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    np.random.seed(42)

    # Generate imbalanced data
    n_majority, n_minority = 1000, int(1000 / imbalance_ratio)
    X = np.vstack([
        np.random.randn(n_majority, 5),
        np.random.randn(n_minority, 5)
    ])
    y = np.hstack([np.zeros(n_majority), np.ones(n_minority)])

    # Create and save DataFrame
    df = pd.DataFrame(X, columns=[f"feature_{i}" for i in range(5)])
    df["target"] = y.astype(int)
    df.to_csv(output_path, index=False)

    print(f"Generated dataset: {len(df)} samples, ratio {imbalance_ratio}:1")
    return output_path

def main():
    """Main function to run the class imbalance agent."""
    parser = argparse.ArgumentParser(description="Class Imbalance Agent")
    parser.add_argument("--file", type=str, help="Path to the CSV file")
    parser.add_argument("--target", type=str, help="Name of the target column")
    parser.add_argument("--output", type=str, default="output", help="Directory to save outputs")
    parser.add_argument("--generate-sample", action="store_true", help="Generate a sample dataset")
    parser.add_argument("--imbalance-ratio", type=float, default=10.0,
                        help="Imbalance ratio for the sample dataset (majority:minority)")

    args = parser.parse_args()

    # Load Groq API key from .env file
    load_env_file()

    # Create output directory
    os.makedirs(args.output, exist_ok=True)

    # Generate sample dataset if requested
    if args.generate_sample:
        sample_path = os.path.join(args.output, "sample_dataset.csv")
        file_path = generate_sample_dataset(sample_path, args.imbalance_ratio)
        target_column = "target"
    else:
        # Use provided file and target column
        if not args.file or not args.target:
            parser.error("--file and --target are required when not using --generate-sample")

        file_path = args.file
        target_column = args.target

    # Run the workflow
    print(f"Running workflow with file: {file_path}, target: {target_column}")
    result = run_workflow(file_path, target_column, args.output)

    # Print the final result
    print("\n" + "="*50)
    print("Workflow completed")
    print("="*50)

    # Print key results
    if result.get("status") == "success":
        print(f"Status: {result.get('status')}")
        print(f"Message: {result.get('message')}")

        if result.get("imbalance_info"):
            print("\nImbalance Information:")
            imbalance_info = result.get("imbalance_info")
            print(f"Is imbalanced: {imbalance_info.get('is_imbalanced')}")
            print(f"Imbalance ratio: {imbalance_info.get('imbalance_ratio'):.2f}")
            print(f"Severity: {imbalance_info.get('severity')}")

        if result.get("recommended_technique"):
            print(f"\nRecommended technique: {result.get('recommended_technique')}")

        if result.get("applied_technique"):
            print(f"\nApplied technique: {result.get('applied_technique')}")
            print(f"Original shape: {result.get('original_shape')}")
            print(f"Resampled shape: {result.get('resampled_shape')}")

        if result.get("visualization_paths"):
            print("\nVisualization paths:")
            for name, path in result.get("visualization_paths").items():
                print(f"- {name}: {path}")

        if result.get("saved_path"):
            print(f"\nResampled data saved to: {result.get('saved_path')}")
    else:
        print(f"Status: {result.get('status')}")
        print(f"Error: {result.get('error')}")

    # Save the full result as JSON
    result_path = os.path.join(args.output, "result.json")

    # Convert DataFrame objects to strings for JSON serialization
    serializable_result = {}
    for key, value in result.items():
        if isinstance(value, pd.DataFrame):
            serializable_result[key] = f"DataFrame with shape {value.shape}"
        else:
            serializable_result[key] = value

    with open(result_path, "w") as f:
        json.dump(serializable_result, f, indent=2)

    print(f"\nFull result saved to: {result_path}")

if __name__ == "__main__":
    main()
