{"save_results": {"file_path": "test_final\\sample_dataset.csv", "target_column": "target", "output_dir": "test_final", "data": "DataFrame/Array with shape (1125, 6)", "distribution": {"class_counts": {"0": 1000, "1": 125}, "class_percentages": {"0": 88.88888888888889, "1": 11.11111111111111}, "total_samples": 1125, "num_classes": 2}, "imbalance_info": {"is_imbalanced": true, "imbalance_ratio": 8.0, "minority_class": 1, "majority_class": 0, "severity": "moderate"}, "recommended_technique": "SMOTE", "resampled_data": "DataFrame/Array with shape (2000, 6)", "applied_technique": "SMOTE", "original_shape": [1125, 6], "resampled_shape": [2000, 6], "visualization_paths": {"original": "test_final\\original_distribution.png", "resampled": "test_final\\resampled_distribution.png"}, "status": "success", "message": "Saved resampled data to test_final\\resampled_data.csv", "saved_path": "test_final\\resampled_data.csv"}}